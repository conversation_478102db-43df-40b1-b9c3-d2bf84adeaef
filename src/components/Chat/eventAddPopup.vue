<template>
  <div v-if="show" class="event-add-popup">
    <div class="event-add-mask"></div>
    <div class="event-add-content">
      <div class="event-add-header">
        <h2 class="event-add-title">添加事件</h2>
        <button class="close-btn" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </button>
      </div>

      <div class="event-add-form">
        <!-- 事件描述 -->
        <div class="form-group">
          <label class="form-label">事件描述</label>
          <textarea
            v-model="formData.description_text"
            class="form-textarea"
            :class="{ error: descriptionError }"
            placeholder="请输入事件描述"
            rows="3"
            @input="descriptionError = false"
          ></textarea>
          <div v-if="descriptionError" class="error-message">请输入事件描述</div>
        </div>

        <!-- 参与者 -->
        <div class="form-group">
          <label class="form-label">参与者</label>
          <div class="participants-input">
            <div class="participant-input-container">
              <input
                v-model="newParticipant"
                type="text"
                class="form-input"
                placeholder="输入参与者姓名"
                @input="handleParticipantInput"
                @blur="hideParticipantSuggestions"
                @keydown.enter.prevent="addParticipant"
              />
              <!-- 参与者建议列表 -->
              <div v-if="showParticipantSuggestions && filteredPersons.length > 0" class="suggestions-list">
                <div
                  v-for="person in filteredPersons"
                  :key="person.person_id"
                  class="suggestion-item"
                  @click="selectParticipant(person.canonical_name)"
                >
                  {{ person.canonical_name }}
                </div>
              </div>
            </div>
            <div v-if="formData.participants && formData.participants.length > 0" class="participant-tags">
              <div v-for="(participant, index) in formData.participants" :key="index" class="participant-tag">
                {{ getDisplayParticipantName(participant) }}
                <button class="remove-tag-btn" @click="removeParticipant(index)">×</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 地点 -->
        <div class="form-group">
          <label class="form-label">地点</label>
          <input v-model="formData.location" type="text" class="form-input" placeholder="请输入地点" />
        </div>

        <!-- 主题 -->
        <div class="form-group">
          <label class="form-label">主题</label>
          <div class="topics-input">
            <input
              v-model="newTopic"
              type="text"
              class="form-input"
              placeholder="输入主题后按回车添加"
              @keydown.enter.prevent="addTopic"
            />
            <div v-if="formData.topics && formData.topics.length > 0" class="topic-tags">
              <div v-for="(topic, index) in formData.topics" :key="index" class="topic-tag">
                {{ topic }}
                <button class="remove-tag-btn" @click="removeTopic(index)">×</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 情感 -->
        <div class="form-group">
          <label class="form-label">情感</label>
          <select v-model="formData.sentiment" class="form-select">
            <option value="">请选择情感</option>
            <option value="positive">积极</option>
            <option value="negative">消极</option>
            <option value="neutral">中性</option>
          </select>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="event-add-actions">
        <button
          class="action-btn save-btn"
          :class="{ 'save-success': saveSuccess }"
          :disabled="saving || saveSuccess"
          @click="handleSave"
        >
          <span v-if="saveSuccess" class="success-icon">✓</span>
          {{ saveSuccess ? '添加成功' : saving ? '添加中...' : '添加' }}
        </button>
        <button class="action-btn cancel-btn" :disabled="saving || saveSuccess" @click="handleClose">取消</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { showSuccessToast, showFailToast } from 'vant';
import type { IAddEventRequest } from '@/apis/memory';
import { addPersonEvent } from '@/apis/memory';
import type { IPersonData } from '@/apis/relation';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personsData?: IPersonData[];
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  save: [];
}>();

// 表单数据
const formData = ref<IAddEventRequest>({
  user_id: props.userId,
  description_text: '',
  participants: [],
  location: '',
  topics: [],
  sentiment: '',
});

// 监听 userId 变化，确保 formData 中的 user_id 始终是最新的
watch(
  () => props.userId,
  (newUserId) => {
    if (newUserId) {
      formData.value.user_id = newUserId;
      console.log('📝 [eventAddPopup.vue] userId 更新:', newUserId);
    }
  },
  { immediate: true },
);

// 表单状态
const saving = ref(false);
const saveSuccess = ref(false);
const descriptionError = ref(false);

// 参与者相关状态
const newParticipant = ref('');
const showParticipantSuggestions = ref(false);

// 主题相关状态
const newTopic = ref('');

// 过滤的人员列表（用于参与者建议）
const filteredPersons = computed(() => {
  if (!props.personsData || !newParticipant.value.trim()) {
    return [];
  }
  const query = newParticipant.value.toLowerCase();
  return props.personsData.filter(
    (person) =>
      person.canonical_name.toLowerCase().includes(query) &&
      !(formData.value.participants || []).includes(person.person_id),
  );
});

// 根据person_id获取显示名称
const getDisplayParticipantName = (participantId: string): string => {
  if (!props.personsData) return participantId;
  const person = props.personsData.find((p) => p.person_id === participantId);
  return person ? person.canonical_name : participantId;
};

// 根据姓名获取person_id
const getPersonIdByName = (name: string): string => {
  if (!props.personsData) return name;
  const person = props.personsData.find((p) => p.canonical_name === name);
  return person ? person.person_id : name;
};

// 添加参与者
const addParticipant = () => {
  const inputName = newParticipant.value.trim();
  if (!inputName) return;

  const personId = getPersonIdByName(inputName);
  if (!formData.value.participants) {
    formData.value.participants = [];
  }
  if (!formData.value.participants.includes(personId)) {
    formData.value.participants.push(personId);
    newParticipant.value = '';
  }
};

// 移除参与者
const removeParticipant = (index: number) => {
  if (formData.value.participants) {
    formData.value.participants.splice(index, 1);
  }
};

// 添加主题
const addTopic = () => {
  if (!formData.value.topics) {
    formData.value.topics = [];
  }
  if (newTopic.value.trim() && !formData.value.topics.includes(newTopic.value.trim())) {
    formData.value.topics.push(newTopic.value.trim());
    newTopic.value = '';
  }
};

// 移除主题
const removeTopic = (index: number) => {
  if (formData.value.topics) {
    formData.value.topics.splice(index, 1);
  }
};

// 处理关闭
const handleClose = () => {
  // 重置表单数据，确保使用最新的 userId
  formData.value = {
    user_id: props.userId || '',
    description_text: '',
    participants: [],
    location: '',
    topics: [],
    sentiment: '',
  };
  // 重置状态
  saving.value = false;
  saveSuccess.value = false;
  descriptionError.value = false;
  newParticipant.value = '';
  newTopic.value = '';
  emit('close');
};

// 处理参与者输入
const handleParticipantInput = () => {
  showParticipantSuggestions.value = true;
};

// 隐藏参与者建议
const hideParticipantSuggestions = () => {
  setTimeout(() => {
    showParticipantSuggestions.value = false;
  }, 200);
};

// 选择参与者
const selectParticipant = (personName: string) => {
  newParticipant.value = personName;
  addParticipant();
  showParticipantSuggestions.value = false;
};

// 处理保存
const handleSave = async () => {
  // 重置错误状态
  descriptionError.value = false;

  // 验证必填字段
  if (!formData.value.description_text?.trim()) {
    descriptionError.value = true;
    return;
  }

  // 确保 user_id 存在
  if (!formData.value.user_id) {
    console.warn('⚠️ [eventAddPopup.vue] user_id 为空，尝试使用 props.userId');
    formData.value.user_id = props.userId || '';

    if (!formData.value.user_id) {
      console.error('❌ [eventAddPopup.vue] user_id 仍然为空，无法添加事件');
      showFailToast({
        message: '用户ID缺失，无法添加事件',
        duration: 3000,
      });
      return;
    }
  }

  saving.value = true;

  try {
    // 准备请求参数
    const addParams: IAddEventRequest = {
      user_id: formData.value.user_id,
      description_text: formData.value.description_text?.trim(),
      participants:
        formData.value.participants && formData.value.participants.length > 0 ? formData.value.participants : undefined,
      location: formData.value.location?.trim() || undefined,
      topics: formData.value.topics && formData.value.topics.length > 0 ? formData.value.topics : undefined,
      sentiment: formData.value.sentiment || undefined,
    };

    console.log('📤 [eventAddPopup.vue] 开始添加事件:', addParams);

    // 调用添加事件API
    const response = await addPersonEvent(addParams);

    console.log('📡 [eventAddPopup.vue] 添加事件响应:', response);

    if (response.result === 'success') {
      showSuccessToast({
        message: '事件添加成功！',
        duration: 3000,
        icon: 'success',
      });

      // 设置成功状态
      saveSuccess.value = true;

      // 延迟关闭弹窗，让用户看到成功状态
      setTimeout(() => {
        emit('save');
        saveSuccess.value = false;
      }, 1500);
    } else {
      showFailToast({
        message: response.reason || '添加失败，请重试',
        duration: 3000,
      });
    }
  } catch (error) {
    console.error('❌ [eventAddPopup.vue] 添加事件失败:', error);
    showFailToast({
      message: '网络错误，添加事件失败，请检查网络连接后重试',
      duration: 4000,
    });
  } finally {
    saving.value = false;
  }
};
</script>

<style lang="scss" scoped>
.event-add-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.event-add-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

.event-add-content {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  height: 800px;
  overflow-y: auto;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

.event-add-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;
  height: 80px;
}

.event-add-title {
  color: white;
  font-size: 36px; // 增加8px (原来28px)
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .close-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
}

.event-add-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
}

.form-label {
  color: white;
  font-size: 30px; // 增加8px (原来22px)
  font-weight: 600;
  margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 20px;
  padding: 18px 22px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 32px; // 增加8px (原来24px)
  line-height: 1.6;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: rgba(0, 188, 212, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;

  &.error {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }
}

.error-message {
  color: #ef4444;
  font-size: 22px; // 增加8px (原来14px)
  margin-top: 4px;
}

.participants-input,
.topics-input {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.participant-input-container {
  position: relative;
}

.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.2s ease;
  color: rgba(255, 255, 255, 0.9);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &:last-child {
    border-bottom: none;
  }
}

.participant-tags,
.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.participant-tag,
.topic-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 188, 212, 0.02);
  color: #00bcd4;
  border: 2px solid #00bcd4;
  border-radius: 16px;
  font-size: 30px; // 增加8px (原来22px)
  font-weight: 500;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: #00bcd4;
  cursor: pointer;
  font-size: 24px; // 增加8px (原来16px)
  font-weight: bold;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 188, 212, 0.3);
  }
}

.event-add-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn {
  flex: 1;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 30px; // 增加8px (原来22px)
  font-weight: 600;
  cursor: pointer;
  border: 2px solid;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 200px;

  &.save-btn {
    color: #00bcd4;
    border-color: #00bcd4;
    background: rgba(0, 188, 212, 0.1);

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.2);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.save-success {
      color: #4caf50;
      border-color: #4caf50;
      background: rgba(76, 175, 80, 0.1);

      &:hover:not(:disabled) {
        background: rgba(76, 175, 80, 0.2);
      }

      &:disabled {
        opacity: 1;
      }
    }
  }

  &.cancel-btn {
    color: rgba(255, 255, 255, 0.7);
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}

.success-icon {
  display: inline-block;
  margin-right: 8px;
  font-size: 26px; // 增加8px (原来18px)
  font-weight: bold;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .event-add-content {
    width: 90%;
    max-width: 600px;
    padding: 20px;
  }

  .event-add-title {
    font-size: 32px; // 增加8px (原来24px)
  }

  .form-label {
    font-size: 26px; // 增加8px (原来18px)
  }

  .form-input,
  .form-textarea,
  .form-select {
    font-size: 28px; // 增加8px (原来20px)
    padding: 14px 18px;
  }

  .participant-tag,
  .topic-tag {
    font-size: 24px; // 增加8px (原来16px)
  }
}
</style>
